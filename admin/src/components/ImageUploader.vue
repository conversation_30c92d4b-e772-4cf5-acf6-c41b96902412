<template>
  <div class="image-uploader">
    <div class="upload-list" v-if="fileList.length > 0">
      <div v-for="(file, index) in fileList" :key="index" class="upload-item">
        <div class="upload-item-content">
          <img :src="file.url" class="uploaded-img" />
          <div class="upload-actions">
            <div class="action-buttons">
              <button type="button" class="btn-crop" @click="openCropper(index)">
                <span>裁切</span>
              </button>
              <button type="button" class="btn-set-primary" @click="setAsPrimary(index)" :class="{ 'primary': file.isPrimary }">
                <span>{{ file.isPrimary ? '主要圖片' : '設為主圖' }}</span>
              </button>
              <button type="button" class="btn-delete" @click="removeFile(index)">
                <span>刪除</span>
              </button>
            </div>
          </div>
        </div>
        <div class="file-info">{{ file.name }}</div>
      </div>
    </div>
    
    <div class="upload-area" @click="triggerFileInput" @drop.prevent="handleDrop" @dragover.prevent>
      <input 
        type="file" 
        ref="fileInput" 
        @change="handleFileChange" 
        multiple 
        accept="image/*" 
        class="file-input" 
      />
      <div class="upload-placeholder">
        <span class="plus-icon">+</span>
        <p>上傳圖片（可多選）</p>
        <p class="upload-hint">建議尺寸 1000×1000 像素，所有圖片將轉為JPG格式</p>
      </div>
    </div>

    <!-- 裁切組件 -->
    <image-cropper
      v-model:open="isCropperVisible"
      :imgUrl="currentImageUrl"
      @cropped="handleCroppedImage"
      @cancel="handleCropperCancel"
    />
  </div>
</template>

<script setup>
import { ref, defineEmits, watch } from 'vue';
import ImageCropper from './ImageCropper.vue';
import imageCompression from 'browser-image-compression';

const props = defineProps({
  value: {
    type: Array,
    default: () => []
  },
  limit: {
    type: Number,
    default: 10
  }
});

const emit = defineEmits(['update:value', 'change']);

const fileInput = ref(null);
const fileList = ref([]);
const isCropperVisible = ref(false);
const currentImageUrl = ref('');
const currentFileIndex = ref(-1);
const pendingFiles = ref([]);
const currentFileIndex2 = ref(0);

// 支援格式白名單
const ALLOWED_TYPES = ['image/jpeg', 'image/png', 'image/jpg', 'image/webp'];

// 監聽外部value變化
watch(() => props.value, (newVal) => {
  if (Array.isArray(newVal)) {
    fileList.value = [...newVal];
  }
}, { deep: true, immediate: true });

// 觸發文件選擇
const triggerFileInput = () => {
  if (fileList.value.length >= props.limit) {
    alert(`最多只能上傳${props.limit}張圖片`);
    return;
  }
  fileInput.value.click();
};

// 處理文件選擇
const handleFileChange = (e) => {
  const files = Array.from(e.target.files);
  if (!files.length) return;

  // 檢查文件數量限制
  if (fileList.value.length + files.length > props.limit) {
    alert(`最多只能上傳${props.limit}張圖片`);
    e.target.value = '';
    return;
  }

  // 檢查格式
  for (const file of files) {
    if (!ALLOWED_TYPES.includes(file.type)) {
      alert('僅支援 PNG、JPEG、JPG、WebP 格式圖片');
      e.target.value = '';
      return;
    }
  }

  // 保存待處理的文件
  pendingFiles.value = [...files];

  // 處理第一個文件
  if (pendingFiles.value.length > 0) {
    currentFileIndex2.value = 0;
    processNextPendingFile();
  }

  e.target.value = '';
};

// 處理拖放
const handleDrop = (e) => {
  const files = Array.from(e.dataTransfer.files).filter(file => file.type.startsWith('image/'));
  if (!files.length) return;

  // 檢查文件數量限制
  if (fileList.value.length + files.length > props.limit) {
    alert(`最多只能上傳${props.limit}張圖片`);
    return;
  }

  // 檢查格式
  for (const file of files) {
    if (!ALLOWED_TYPES.includes(file.type)) {
      alert('僅支援 PNG、JPEG、JPG、WebP 格式圖片');
      return;
    }
  }

  // 保存待處理的文件
  pendingFiles.value = [...files];

  // 處理第一個文件
  if (pendingFiles.value.length > 0) {
    currentFileIndex2.value = 0;
    processNextPendingFile();
  }
};

// 處理下一個待處理文件
const processNextPendingFile = async () => {
  if (currentFileIndex2.value < pendingFiles.value.length) {
    const file = pendingFiles.value[currentFileIndex2.value];
    if (!file.type.startsWith('image/')) {
      currentFileIndex2.value++;
      processNextPendingFile();
      return;
    }
    // 格式檢查
    if (!ALLOWED_TYPES.includes(file.type)) {
      alert('僅支援 PNG、JPEG、JPG、WebP 格式圖片');
      currentFileIndex2.value++;
      processNextPendingFile();
      return;
    }
    // 圖片壓縮與尺寸優化
    let compressedFile = file;
    try {
      compressedFile = await imageCompression(file, {
        maxWidthOrHeight: 1920,
        initialQuality: 0.85,
        fileType: 'image/jpeg',
        useWebWorker: true
      });
    } catch (err) {
      alert('圖片壓縮失敗，請嘗試其他圖片或稍後再試');
      currentFileIndex2.value++;
      processNextPendingFile();
      return;
    }
    // 產生預覽
    const reader = new FileReader();
    reader.onload = (e) => {
      currentImageUrl.value = e.target.result;
      const tempFile = {
        file: compressedFile,
        name: compressedFile.name || file.name,
        url: e.target.result,
        isPrimary: fileList.value.length === 0, // 第一張圖自動設為主圖
        isNew: true
      };
      const tempIndex = fileList.value.length;
      fileList.value.push(tempFile);
      currentFileIndex.value = tempIndex;
      isCropperVisible.value = true;
    };
    reader.readAsDataURL(compressedFile);
  }
};

// 重新裁切已上傳的圖片
const openCropper = (index) => {
  if (index >= 0 && index < fileList.value.length) {
    currentImageUrl.value = fileList.value[index].url;
    currentFileIndex.value = index;
    isCropperVisible.value = true;
  }
};

// 移除文件
const removeFile = (index) => {
  fileList.value.splice(index, 1);
  
  // 如果移除的是主圖，將第一張圖設為主圖
  if (fileList.value.length > 0 && !fileList.value.some(file => file.isPrimary)) {
    fileList.value[0].isPrimary = true;
  }
  
  updateValue();
};

// 設置主圖
const setAsPrimary = (index) => {
  fileList.value.forEach((file, i) => {
    file.isPrimary = i === index;
  });
  
  updateValue();
};

// 處理裁切後的圖片
const handleCroppedImage = (data) => {
  if (currentFileIndex.value >= 0 && currentFileIndex.value < fileList.value.length) {
    // 更新現有圖片
    fileList.value[currentFileIndex.value].url = data;
    fileList.value[currentFileIndex.value].croppedImage = data;
    
    // 處理下一個待處理文件
    currentFileIndex2.value++;
    processNextPendingFile();
  }
  
  updateValue();
};

// 處理裁切取消
const handleCropperCancel = () => {
  // 如果是處理新上傳的文件，取消後應該從列表中刪除
  if (currentFileIndex.value >= 0 && 
      currentFileIndex.value < fileList.value.length &&
      fileList.value[currentFileIndex.value].isNew &&
      !fileList.value[currentFileIndex.value].croppedImage) {
    fileList.value.splice(currentFileIndex.value, 1);
  }
  
  // 嘗試處理下一個文件
  currentFileIndex2.value++;
  processNextPendingFile();
  
  updateValue();
};

// 更新值
const updateValue = () => {
  emit('update:value', fileList.value);
  emit('change', fileList.value);
};
</script>

<style scoped>
.image-uploader {
  width: 100%;
}

.upload-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.upload-item {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.upload-item-content {
  position: relative;
  width: 100%;
  padding-top: 100%; /* 1:1 aspect ratio */
  overflow: hidden;
}

.uploaded-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
}

.upload-item:hover .uploaded-img {
  transform: scale(1.05);
}

.upload-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  padding: 5px;
}

.action-buttons {
  display: flex;
  gap: 5px;
  width: 100%;
  justify-content: center;
}

.btn-crop {
  background-color: #f59e0b;
  color: white;
  border: none;
  font-size: 0.75rem;
  padding: 2px 6px;
  border-radius: 4px;
  cursor: pointer;
  margin-right: 2px;
}

.btn-crop:hover {
  background-color: #d97706;
}

.btn-delete {
  background-color: #ef4444;
  color: white;
  border: none;
  font-size: 0.75rem;
  padding: 2px 6px;
  border-radius: 4px;
  cursor: pointer;
}

.btn-delete:hover {
  background-color: #dc2626;
}

.btn-set-primary {
  background-color: #10b981;
  color: white;
  border: none;
  font-size: 0.75rem;
  padding: 2px 6px;
  border-radius: 4px;
  cursor: pointer;
}

.btn-set-primary:hover {
  background-color: #059669;
}

.btn-set-primary.primary {
  background-color: #0369a1;
}

.file-info {
  padding: 8px;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  background-color: #f8f8f8;
  text-align: center;
}

.upload-area {
  border: 2px dashed #ccc;
  border-radius: 4px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.3s;
  position: relative;
}

.upload-area:hover {
  border-color: #4a7aff;
}

.file-input {
  position: absolute;
  width: 0;
  height: 0;
  opacity: 0;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.plus-icon {
  font-size: 40px;
  font-weight: 100;
  margin-bottom: 10px;
  color: #999;
}

.upload-hint {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}
</style> 