<template>
  <div class="product-create-page">
    <div class="page-header">
    </div>

    <div class="product-form">
      <form @submit.prevent="handleSubmit">
        <div class="form-grid">
          <!-- 圖片上傳 (橫跨) -->
          <div class="grid-section full-width">
             <h4 class="section-title">商品圖片</h4>
            <div class="image-upload">
              <image-uploader
                v-model:value="imageFiles"
                @change="handleImageChange"
                :limit="10"
              />
            </div>
          </div>

          <!-- 商品基本資訊 (左欄) -->
          <div class="grid-section">
             <h4 class="section-title">基本資訊</h4>
            <div class="form-group">
              <label>商品名稱 <span class="required">*</span></label>
              <input v-model="product.name" type="text" class="form-control" placeholder="請輸入商品名稱" required autocomplete="off" />
            </div>

            <div class="form-group">
              <label>商品規格 <span class="required">*</span></label>
              <input v-model="product.spec" type="text" class="form-control" placeholder="請輸入商品規格" required autocomplete="off" />
            </div>

            <div class="form-group">
              <label>排序</label>
              <input v-model.number="product.sort_order" type="number" class="form-control" placeholder="請輸入排序值" autocomplete="off" />
              <small class="form-text">數值越大排序越前面，可輸入負值，預設為0</small>
            </div>

            <div class="form-row">
              <div class="form-group half">
                <label>SKU <span class="required">*</span></label>
                <input v-model="product.sku" type="text" class="form-control" placeholder="請輸入 SKU" required />
              </div>
              <div class="form-group half">
                <label>商品顏色</label>
                <input v-model="product.color" type="text" class="form-control" placeholder="請輸入商品顏色 (選填)" />
              </div>
            </div>

            <div class="form-group">
              <label>商品分類 <span class="required">*</span></label>
              <el-cascader
                v-model="categoryCascaderValue"
                :options="categoryTreeData"
                :props="{
                  checkStrictly: false,
                  value: 'id',
                  label: 'name',
                  children: 'children',
                  emitPath: false,
                  expandTrigger: 'hover'
                }"
                placeholder="請選擇商品分類"
                clearable
                filterable
                class="form-control"
                style="width: 100%;"
                @change="handleCategoryChange"
              />
            </div>
          </div>

          <!-- 右欄 -->
          <div class="grid-section">
             <!-- 價格相關 -->
            <div> <!-- 新增 wrapper div -->
            <h4 class="section-title">價格</h4>
            <div class="form-group">
              <label>零售價</label>
              <div class="price-input">
                <div class="currency-box">$</div>
                <input v-model.number="product.retail_price" type="number" class="form-control" placeholder="0" min="0" autocomplete="off" />
              </div>
            </div>

            <div class="form-group">
              <label>成本</label>
              <div class="price-input">
                <div class="currency-box">$</div>
                <input v-model.number="product.cost_price" type="number" class="form-control" placeholder="0" min="0" autocomplete="off" />
              </div>
            </div>
            </div>

             <!-- 商品介紹 -->
            <div> <!-- 新增 wrapper div -->
              <h4 class="section-title">商品介紹</h4>
            <div class="form-group">
              <label>商品介紹</label>
                <textarea v-model="product.description" class="form-control" rows="5" placeholder="請輸入商品介紹" autocomplete="off"></textarea>
              </div>
            </div>
          </div>
        </div>

        <div class="form-actions">
          <button type="submit" class="btn btn-primary" :disabled="isSubmitting">
            {{ isSubmitting ? '處理中...' : '建立新商品' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue';
import { ElCascader } from 'element-plus';
import axios from 'axios';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import ImageUploader from '../components/ImageUploader.vue';

const router = useRouter();
const fileInput = ref(null);
const selectedImages = ref([]);
const imagePreviewUrls = ref([]);
const categories = ref([]);
const categoryTreeData = ref([]);
const categoryCascaderValue = ref(null);
const isSubmitting = ref(false);
const imageFiles = ref([]);

const product = reactive({
  name: '',
  spec: '',
  sku: '',
  color: '',
  category_id: '',
  retail_price: null,
  cost_price: null,
  description: '',
  sort_order: 0
});

function buildCategoryTree(flatData) {
  const map = {};
  flatData.forEach(item => {
    map[item.id] = { ...item, id: item.id, name: item.name, children: [] };
  });

  const tree = [];
  flatData.forEach(item => {
    const node = map[item.id];
    if (item.parent_id) {
      if (map[item.parent_id]) {
        map[item.parent_id].children.push(node);
        map[item.parent_id].children.sort((a, b) => (a.display_order ?? 0) - (b.display_order ?? 0));
      } else {
        tree.push(node);
      }
    } else {
      tree.push(node);
    }
  });

  tree.sort((a, b) => (a.display_order ?? 0) - (b.display_order ?? 0));
  return tree;
}

onMounted(async () => {
  try {
    const response = await axios.get('/admin/categories');
    let flatData = [];
    if (response.data.flat && Array.isArray(response.data.flat)) {
      flatData = response.data.flat;
    } else if (Array.isArray(response.data)) {
      flatData = response.data;
    } else {
      console.error('不支持的分類 API 響應格式:', typeof response.data);
      return;
    }
    categories.value = flatData;
    categoryTreeData.value = buildCategoryTree(flatData);

    // 從路由查詢參數中獲取預設的分類ID
    const { categoryId } = router.currentRoute.value.query;
    if (categoryId) {
      // 設置預設分類
      const categoryIdNum = Number(categoryId);
      product.category_id = categoryIdNum;
      categoryCascaderValue.value = categoryIdNum;

      console.log(`從列表頁自動選擇分類ID: ${categoryIdNum}`);

      // 找到分類名稱並顯示提示
      const selectedCategory = categories.value.find(c => c.id === categoryIdNum);
      if (selectedCategory) {
        ElMessage.success(`已自動選擇分類: ${selectedCategory.name}`);
      }
    }
  } catch (error) {
    console.error('獲取分類列表失敗:', error);
  }
});

function triggerFileInput() {
  fileInput.value.click();
}

function handleImageChange(files) {
  // 新的圖片處理函數，接收裁切後的圖片數據
  console.log('圖片已更新，共', files.length, '張');
}

// 輔助函數：將 Data URL 轉換為 File 對象
function dataURLtoFile(dataurl, filename) {
  // 從 base64 字符串中獲取數據類型
  const arr = dataurl.split(',');
  const mime = 'image/jpeg'; // 強制設置為JPEG格式
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);

  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }

  // 確保檔名以.jpg結尾
  const filenameWithoutExt = filename.replace(/\.[^/.]+$/, "");
  return new File([u8arr], `${filenameWithoutExt}.jpg`, { type: mime });
}

function handleCategoryChange(value) {
  product.category_id = typeof value === 'number' ? value : Number(value);
  console.log("選中的分類ID:", product.category_id);
}

// 生成唯一的文件名
const generateUniqueFilename = (index) => {
  // 使用sku、時間戳和隨機字符串的組合，確保唯一性
  const timestamp = Date.now();
  const randomStr = Math.random().toString(36).substring(2, 8);
  const baseFilename = product.sku ?
    `${product.sku}_${timestamp}_${randomStr}_${index}` :
    `newproduct_${timestamp}_${randomStr}_${index}`;

  return `${baseFilename}.jpg`;
};

const handleSubmit = async () => {
  // 驗證必填欄位
  const missingFields = [];
  if (!product.name.trim()) missingFields.push('產品名稱');
  if (!product.spec.trim()) missingFields.push('規格');
  if (!product.sku.trim()) missingFields.push('SKU');
  if (!product.category_id) missingFields.push('產品分類');

  if (missingFields.length > 0) {
    ElMessage.error(`以下欄位為必填項: ${missingFields.join(', ')}`);
    return;
  }

  try {
    isSubmitting.value = true;

    // 準備表單數據
    const formData = new FormData();
    formData.append('name', product.name);
    formData.append('spec', product.spec.trim());
    formData.append('specification', product.spec.trim());
    formData.append('spec_name', product.spec.trim());
    formData.append('sku', product.sku);
    formData.append('color', product.color);
    formData.append('category_id', product.category_id);
    formData.append('retail_price', product.retail_price);
    formData.append('cost_price', product.cost_price);
    formData.append('description', product.description);
    formData.append('sort_order', product.sort_order || 0);

    // 處理裁切後的圖片 - 使用Promise.all處理非同步轉換
    if (imageFiles.value.length > 0) {
      const imagePromises = imageFiles.value.map((imageData, i) => {
        return new Promise((resolve) => {
          // 生成唯一檔名
          const uniqueFilename = generateUniqueFilename(i + 1);

          // 處理裁切後的圖片
          if (imageData.croppedImage) {
            const file = dataURLtoFile(
              imageData.croppedImage,
              uniqueFilename
            );
            resolve({ file, isPrimary: imageData.isPrimary, index: i });
          }
          // 處理原始文件
          else if (imageData.file) {
            const reader = new FileReader();
            reader.onload = (e) => {
              const file = dataURLtoFile(
                e.target.result,
                uniqueFilename
              );
              resolve({ file, isPrimary: imageData.isPrimary, index: i });
            };
            reader.readAsDataURL(imageData.file);
          } else {
            resolve(null);
          }
        });
      });

      // 等待所有圖片處理完成
      const processedImages = await Promise.all(imagePromises);

      // 找出主圖索引，如果沒有主圖則第一張設為主圖
      let primaryImageIndex = -1;
      processedImages.forEach((img, arrayIndex) => {
        if (img && img.isPrimary) {
          primaryImageIndex = arrayIndex;
        }
      });

      // 如果沒有主圖，第一張圖片設為主圖
      if (primaryImageIndex === -1 && processedImages.length > 0) {
        primaryImageIndex = 0;
      }

      // 添加處理完成的圖片到formData - 全部作為images處理，讓後端決定主圖
      processedImages.forEach((img, arrayIndex) => {
        if (img && img.file) {
          formData.append('images', img.file);
        }
      });
    }

    console.log('準備提交的資料:', {
      name: product.name,
      spec: product.spec,
      sku: product.sku,
      color: product.color,
      category_id: product.category_id,
      retail_price: product.retail_price,
      cost_price: product.cost_price,
      description: product.description,
      images: imageFiles.value.length ? `已上傳 ${imageFiles.value.length} 張圖片` : '無圖片'
    });

    try {
      const response = await axios.post('/admin/products', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      console.log('商品創建成功:', response.data);
      ElMessage.success('產品創建成功！');
      resetForm();

    } catch (apiError) {
      console.error('API請求失敗:', apiError);

      let errorMessage = '創建產品時發生錯誤';

      if (apiError.response) {
        // 處理不同的錯誤狀態碼
        const status = apiError.response.status;
        const errorData = apiError.response.data;

        switch (status) {
          case 400:
            errorMessage = '請求資料格式不正確，請檢查所有欄位';
            break;
          case 409:
            errorMessage = '產品名稱或SKU已存在，請修改後重試';
            break;
          case 413:
            errorMessage = '上傳的圖片檔案太大，請壓縮後重新上傳';
            break;
          case 401:
            errorMessage = '您沒有權限執行此操作，請重新登入';
            break;
          case 500:
            // 檢查是否為SKU重複錯誤
            const responseError = errorData?.error || '';
            if (responseError.toLowerCase().includes('sku') &&
                (responseError.toLowerCase().includes('unique') ||
                 responseError.toLowerCase().includes('duplicate') ||
                 responseError.toLowerCase().includes('already exists') ||
                 responseError.toLowerCase().includes('重複') ||
                 responseError.toLowerCase().includes('已存在'))) {
              errorMessage = '此SKU已使用，請變更SKU編碼';
            } else {
              errorMessage = '伺服器內部錯誤，請稍後再試';
            }
            break;
        }

        // 如果有詳細錯誤信息，但非SKU重複錯誤，優先使用
        if (errorData && errorData.error && !errorMessage.includes('此SKU已使用')) {
          errorMessage = errorData.error;
        }
      } else if (apiError.request) {
        // 請求已發送但沒有收到回應
        errorMessage = '伺服器無回應，請確認網路連接';
      } else {
        // 設置請求時發生錯誤
        errorMessage = apiError.message || '未知錯誤';
      }

      ElMessage.error(errorMessage);
    }

  } catch (error) {
    console.error('提交產品時發生錯誤:', error);
    ElMessage.error(error.message || '創建產品時發生未知錯誤');
  } finally {
    isSubmitting.value = false;
  }
};

const resetForm = () => {
  product.name = '';
  product.spec = '';
  product.sku = '';
  product.color = '';
  product.category_id = '';
  product.retail_price = null;
  product.cost_price = null;
  product.description = '';
  product.sort_order = 0;
  selectedImages.value = [];
  imagePreviewUrls.value = [];
  categoryCascaderValue.value = null;
  imageFiles.value = [];
};
</script>

<style scoped>
.product-create-page {
  padding: 25px;
  background-color: #f9fafb;
}

.page-header {
  margin-bottom: 20px;
}

.product-form {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  padding: 25px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 25px;
}

.grid-section {
  /* padding: 8px; */
}

.full-width {
  grid-column: 1 / -1;
}

.form-group {
  margin-bottom: 18px;
}

.form-row {
  display: flex;
  gap: 15px;
}

.form-row .form-group.half {
  flex: 1;
  margin-bottom: 0;
}

label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  font-size: 0.875rem;
  color: #4b5563;
}

.required {
  color: #ef4444;
  margin-left: 2px;
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-control:focus {
  outline: none;
  border-color: #4a6cf7;
  box-shadow: 0 0 0 2px rgba(74, 108, 247, 0.2);
}

.form-text {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 4px;
  display: block;
}

textarea.form-control {
  min-height: 100px;
  resize: vertical;
}

.image-upload {
  width: 100%;
}

.upload-area {
  width: 100%;
  height: 200px;
  border: 2px dashed #d1d5db;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  overflow: hidden;
  background-color: #f9fafb;
  transition: border-color 0.2s, background-color 0.2s;
}

.upload-area:hover {
  border-color: #4a6cf7;
  background-color: #f0f3ff;
}

.upload-placeholder {
  text-align: center;
  color: #6b7280;
}

.plus-icon {
  display: block;
  font-size: 30px;
  margin-bottom: 10px;
  color: #9ca3af;
}

.upload-placeholder p {
  font-size: 0.875rem;
}

.images-preview-container {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  height: 100%;
  padding: 8px;
  overflow-y: auto;
}

.image-preview {
  width: 100px;
  height: 100px;
  object-fit: cover;
  margin: 5px;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

.selected-images-info {
  margin-top: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
  color: #6b7280;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 0.75rem;
}

.btn-secondary {
  background-color: #9ca3af;
  color: white;
  border: none;
}

.btn-secondary:hover {
  background-color: #6b7280;
}

.price-input {
  display: flex;
  align-items: center;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  overflow: hidden;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.price-input:focus-within {
  border-color: #4a6cf7;
  box-shadow: 0 0 0 2px rgba(74, 108, 247, 0.2);
}

.currency-box {
  background-color: #f3f4f6;
  padding: 8px 12px;
  border-right: 1px solid #d1d5db;
  font-size: 0.875rem;
  color: #4b5563;
  line-height: 1.5;
}

.price-input .form-control {
  border: none;
  padding: 8px 12px;
  flex-grow: 1;
  line-height: 1.5;
  border-radius: 0;
}

.price-input .form-control:focus {
  box-shadow: none;
}

.price-input input[type=number]::-webkit-inner-spin-button,
.price-input input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.price-input input[type=number] {
  -moz-appearance: textfield;
}

.section-title {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 8px;
}

.form-actions {
  margin-top: 30px;
  text-align: right;
}

.btn {
  padding: 10px 20px;
  font-size: 0.9rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s, box-shadow 0.2s;
  font-weight: 500;
}

.btn-primary {
  background-color: #4a6cf7;
  color: white;
}

.btn-primary:hover {
  background-color: #3a5cd7;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.btn-primary:disabled {
  background-color: #a0aec0;
  cursor: not-allowed;
  box-shadow: none;
}

/* Element Plus Cascader 樣式微調 */
.form-control.el-cascader {
  padding: 0;
  border: none;
}

:deep(.el-cascader .el-input__wrapper) {
  padding: 0;
  box-shadow: none !important;
  border-radius: 6px;
  border: 1px solid #d1d5db;
}

:deep(.el-cascader .el-input__inner) {
  padding: 8px 12px;
  height: auto;
  line-height: 1.5;
  font-size: 0.875rem;
}

:deep(.el-input.is-focus .el-input__wrapper) {
  border-color: #4a6cf7;
  box-shadow: 0 0 0 2px rgba(74, 108, 247, 0.2) !important;
}

:deep(.el-cascader .el-input__inner::placeholder) {
    color: #9ca3af;
}

:deep(.el-cascader-node__radio) {
  display: none !important;
}

:deep(.el-cascader-node) {
  padding-left: 15px !important;
}

:deep(.el-cascader-node__label) {
  flex: 1;
  cursor: pointer;
}

.images-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 15px;
}

.image-item {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.image-item .image-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
  margin: 0;
}

.image-status {
  position: absolute;
  top: 5px;
  right: 5px;
  z-index: 5;
}

.image-badge {
  color: white;
  font-size: 0.75rem;
  background-color: #4a6cf7;
  padding: 2px 6px;
  border-radius: 4px;
}

h5 {
  font-size: 1rem;
  margin-bottom: 10px;
  color: #4b5563;
}
</style>