const express = require('express');
const path = require('path');
const multer = require('multer');
const fs = require('fs');

// 熱賣商品快取
let hotProductsCache = {
  data: null,
  lastUpdate: null,
  ttl: 5 * 60 * 1000 // 5分鐘快取
};

// 生成檔案名稱（直接使用SKU，不添加CGP前綴）
function generateFilename(sku) {
  // 如果有SKU，直接返回SKU，否則使用時間戳
  if (sku) {
    return sku;
  } else {
    // 如果沒有SKU，使用時間戳作為檔案名
    const timestamp = Date.now();
    return `product_${timestamp}`;
  }
}

// 使用依賴注入模式，與其他路由一致
module.exports = ({ productDb, logError }) => {
  const router = express.Router();

  // 設定 multer 儲存配置
  const storage = multer.diskStorage({
    destination: function(req, file, cb) {
      const uploadDir = path.join(__dirname, '..', 'public', 'uploads', 'products');
      // 確保目錄存在
      if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
      }
      cb(null, uploadDir);
    },
    filename: function(req, file, cb) {
      // 使用SKU作為檔案名稱
      const sku = req.body.sku || req.query.sku;
      const ext = path.extname(file.originalname) || '.jpg';
      const filename = generateFilename(sku) + ext;

      // 處理非法字元
      const safeFilename = filename.replace(/[^a-zA-Z0-9.-_]/g, '_');
      cb(null, safeFilename);
    }
  });

  const upload = multer({
    storage: storage,
    limits: { fileSize: 5 * 1024 * 1024 }, // 限制 5MB
    fileFilter: function(req, file, cb) {
      // 僅接受圖片檔案
      if (file.mimetype.startsWith('image/')) {
        cb(null, true);
      } else {
        cb(new Error('只允許上傳圖片檔案'));
      }
    }
  });

  // 獲取商品列表（支援最新商品和熱賣商品）
  router.get('/', (req, res) => {
    // 支援分頁查詢
    const page = parseInt(req.query.page) || 1;
    // 如果沒有指定分類，則限制為最新50個商品
    const limit = parseInt(req.query.limit) || (req.query.category ? 20 : 50);
    const offset = (page - 1) * limit;

    // 檢查是否為熱賣商品查詢且可以使用快取
    const isHotSalesQuery = req.query.sort === 'hot_sales' && !req.query.category && !req.query.search;
    const now = Date.now();

    if (isHotSalesQuery && hotProductsCache.data &&
        hotProductsCache.lastUpdate &&
        (now - hotProductsCache.lastUpdate) < hotProductsCache.ttl) {
      // 使用快取數據
      const cachedData = hotProductsCache.data;
      const startIndex = offset;
      const endIndex = offset + limit;
      const paginatedData = cachedData.slice(startIndex, endIndex);

      return res.json({
        data: paginatedData,
        total: cachedData.length,
        page: page,
        limit: limit,
        totalPages: Math.ceil(cachedData.length / limit)
      });
    }

    // 支援分類過濾（包含子分類）
    let categoryFilter = '';
    const categoryId = req.query.category ? parseInt(req.query.category) : null;

    // 支援排序
    // 如果沒有指定分類或分類為null，根據sort參數決定排序方式
    // hot_sales: 熱賣商品（過去7天銷售量排序）
    // created_at: 最新商品（創建日期排序）
    // 如果有指定分類，則按照自訂排序順序
    let sortBy = req.query.sort;
    if (!sortBy) {
      // 沒有指定排序時，根據是否有分類來決定預設排序
      sortBy = categoryId ? 'sort_order' : 'hot_sales';
    }
    const sortOrder = req.query.order === 'asc' ? 'ASC' : 'DESC';

    // 檢查是否是按零售價格排序
    const isRetailPriceSort = sortBy === 'retail_price';
    // 檢查是否是按熱賣商品排序
    const isHotSalesSort = sortBy === 'hot_sales';

    // 如果是按零售價格或熱賣商品排序，我們將在SQL中特殊處理
    if (isRetailPriceSort || isHotSalesSort) {
      // 將sortBy設為一個不會用於直接排序的值，後面會特殊處理
      sortBy = 'id';
    }

    if (categoryId) {
      // 先查詢指定分類的所有子分類（包括多層級的子分類）
      productDb.all(`
        WITH RECURSIVE category_tree AS (
          SELECT id, name, parent_id FROM category WHERE id = ?
          UNION ALL
          SELECT c.id, c.name, c.parent_id FROM category c
          JOIN category_tree ct ON c.parent_id = ct.id
        )
        SELECT id FROM category_tree
      `, [categoryId], (err, categories) => {
        if (err) {
          console.error('查詢分類樹失敗:', err);
          logError(err, req, res, '查詢分類樹失敗');
          return res.status(500).json({ error: '獲取商品列表失敗' });
        }

        // 取出所有分類ID，包括傳入的分類ID和所有子分類ID
        const categoryIds = categories.map(c => c.id);

        if (categoryIds.length > 0) {
          const placeholders = categoryIds.map(() => '?').join(',');
          categoryFilter = `AND EXISTS (SELECT 1 FROM product_category WHERE product_id = p.id AND category_id IN (${placeholders}))`;

          // 繼續查詢商品
          continueProductQuery(categoryIds);
        } else {
          // 如果沒有找到任何分類，則僅使用當前分類ID
          categoryFilter = `AND EXISTS (SELECT 1 FROM product_category WHERE product_id = p.id AND category_id = ?)`;
          continueProductQuery([categoryId]);
        }
      });
    } else {
      // 沒有指定分類，不需要分類過濾
      continueProductQuery([]);
    }

    // 繼續查詢商品的函數
    function continueProductQuery(categoryIdParams) {
      // 支援搜索
      const searchTerm = req.query.search ? req.query.search.trim() : '';
      const searchFilter = searchTerm ? `AND (p.name LIKE '%${searchTerm}%' OR p.description LIKE '%${searchTerm}%' OR p.spec_name LIKE '%${searchTerm}%')` : '';

      // 構建計算總記錄數的SQL
      let countSQL = `
        SELECT COUNT(*) as total
        FROM product p
        WHERE p.is_active = 1 ${searchFilter}
      `;

      let countParams = [];

      if (categoryFilter && categoryIdParams.length > 0) {
        countSQL += ` ${categoryFilter}`;
        countParams = categoryIdParams;
      }

      // 計算總記錄數
      productDb.get(countSQL, countParams, (err, countResult) => {
        if (err) {
          console.error('獲取商品總數失敗:', err);
          logError(err, req, res, '獲取商品總數失敗');
          return res.status(500).json({ error: '獲取商品列表失敗' });
        }

        const total = countResult.total;

        // 如果沒有記錄，直接返回空結果
        if (total === 0) {
          return res.json({
            total: 0,
            page,
            limit,
            totalPages: 0,
            data: []
          });
        }

        // 構建獲取商品的SQL - 優化版本使用LEFT JOIN
        let productSQL = `
          SELECT
            p.*,
            b.name as brand_name,
            COALESCE(
              (SELECT image_path FROM product_image WHERE product_id = p.id AND is_primary = 1 LIMIT 1),
              (SELECT image_path FROM product_image WHERE product_id = p.id ORDER BY display_order ASC LIMIT 1)
            ) as main_image,
            (SELECT category_id FROM product_category WHERE product_id = p.id LIMIT 1) as category_id,
            (SELECT price FROM product_price WHERE product_id = p.id AND price_type_id = 2 ORDER BY effective_date DESC LIMIT 1) as retail_price,
            COALESCE(sales_data.sales_volume_7days, 0) as sales_volume_7days
          FROM product p
          LEFT JOIN brand b ON p.brand_id = b.id
          LEFT JOIN (
            SELECT
              oi.product_id,
              SUM(oi.quantity) as sales_volume_7days
            FROM order_item oi
            JOIN "order" o ON oi.order_id = o.id
            WHERE o.order_status_id IN (3, 4, 5) AND o.order_status_id NOT IN (6, 7, 8, 9, 10)
              AND o.order_date >= datetime('now', '-7 days', '+8 hours')
            GROUP BY oi.product_id
          ) sales_data ON p.id = sales_data.product_id
          WHERE p.is_active = 1 ${searchFilter}
        `;

        let productParams = [];

        if (categoryFilter && categoryIdParams.length > 0) {
          productSQL += ` ${categoryFilter}`;
          productParams = categoryIdParams;
        }

        // 處理排序
        if (isRetailPriceSort) {
          // 如果是按零售價格排序，使用子查詢結果排序
          productSQL += ` ORDER BY (SELECT price FROM product_price WHERE product_id = p.id AND price_type_id = 2 ORDER BY effective_date DESC LIMIT 1) ${sortOrder} LIMIT ? OFFSET ?`;
        } else if (isHotSalesSort) {
          // 如果是按熱賣商品排序，按銷售量DESC，銷售量相同時按創建時間DESC
          productSQL += ` ORDER BY sales_volume_7days DESC, p.created_at DESC LIMIT ? OFFSET ?`;
        } else {
          // 其他欄位正常排序
          if (req.query.order === undefined) {
            // 沒有指定排序方向時的預設邏輯
            if (sortBy === 'created_at') {
              // 創建日期預設為DESC（最新的在前面）
              productSQL += ` ORDER BY p.${sortBy} DESC LIMIT ? OFFSET ?`;
            } else if (sortBy === 'sort_order') {
              // 自訂排序預設為DESC（數字越大越前面）
              productSQL += ` ORDER BY p.${sortBy} DESC LIMIT ? OFFSET ?`;
            } else {
              productSQL += ` ORDER BY p.${sortBy} ${sortOrder} LIMIT ? OFFSET ?`;
            }
          } else {
            productSQL += ` ORDER BY p.${sortBy} ${sortOrder} LIMIT ? OFFSET ?`;
          }
        }
        productParams.push(limit, offset);

        // 獲取分頁數據
        productDb.all(productSQL, productParams, (err, products) => {
          if (err) {
            console.error('獲取商品列表失敗:', err);
            logError(err, req, res, '獲取商品列表失敗');
            return res.status(500).json({ error: '獲取商品列表失敗' });
          }

          // 轉換數據類型
          products.forEach(product => {
            // 轉換 category_id 為數字
            if (product.category_id) {
              product.category_id = Number(product.category_id);
            }

            // 轉換 retail_price 為數字
            if (product.retail_price) {
              product.retail_price = Number(product.retail_price);
            }
          });

          // 獲取所有商品的庫存信息
          const productIds = products.map(p => p.id);

          // 如果沒有產品，直接返回空數組
          if (productIds.length === 0) {
            return res.json({
              total,
              page,
              limit,
              totalPages: Math.ceil(total / limit),
              data: []
            });
          }

          // 使用 IN 操作符來批量查詢庫存
          const placeholders = productIds.map(() => '?').join(',');
          const stockQuery = `
            SELECT i.product_id, i.quantity, w.name as warehouse_name
            FROM inventory i
            JOIN warehouse w ON i.warehouse_id = w.id
            WHERE i.product_id IN (${placeholders}) AND w.name LIKE '%航線台北倉%'
          `;

          productDb.all(stockQuery, productIds, (err, stockData) => {
            if (err) {
              console.error('獲取商品庫存失敗:', err);
              logError(err, req, res, '獲取商品庫存失敗');
              // 即使獲取庫存失敗，仍然返回商品列表，只是沒有庫存信息
              return res.json({
                total,
                page,
                limit,
                totalPages: Math.ceil(total / limit),
                data: products
              });
            }

            // 建立產品ID到庫存的映射
            const stockMap = {};
            stockData.forEach(item => {
              stockMap[item.product_id] = {
                stock: item.quantity,
                warehouse_name: item.warehouse_name
              };
            });

            // 將庫存信息添加到商品數據中
            products.forEach(product => {
              const stockInfo = stockMap[product.id];
              if (stockInfo) {
                product.stock = stockInfo.stock;
                product.warehouse_name = stockInfo.warehouse_name;
              } else {
                product.stock = 0;
              }
            });

            // 如果是熱賣商品查詢且沒有分頁（獲取全部數據），更新快取
            if (isHotSalesQuery && page === 1 && limit >= 50) {
              // 獲取完整的熱賣商品數據用於快取
              const fullHotProductsSQL = productSQL.replace('LIMIT ? OFFSET ?', '');
              const fullParams = productParams.slice(0, -2); // 移除 limit 和 offset 參數

              productDb.all(fullHotProductsSQL, fullParams, (err, allHotProducts) => {
                if (!err && allHotProducts) {
                  // 處理庫存信息
                  const allProductIds = allHotProducts.map(p => p.id);
                  if (allProductIds.length > 0) {
                    const allPlaceholders = allProductIds.map(() => '?').join(',');
                    const allStockQuery = `
                      SELECT i.product_id, i.quantity, w.name as warehouse_name
                      FROM inventory i
                      JOIN warehouse w ON i.warehouse_id = w.id
                      WHERE i.product_id IN (${allPlaceholders}) AND w.name LIKE '%航線台北倉%'
                    `;

                    productDb.all(allStockQuery, allProductIds, (err, allStockData) => {
                      if (!err) {
                        const allStockMap = {};
                        allStockData.forEach(item => {
                          allStockMap[item.product_id] = {
                            stock: item.quantity,
                            warehouse_name: item.warehouse_name
                          };
                        });

                        allHotProducts.forEach(product => {
                          const stockInfo = allStockMap[product.id];
                          if (stockInfo) {
                            product.stock = stockInfo.stock;
                            product.warehouse_name = stockInfo.warehouse_name;
                          } else {
                            product.stock = 0;
                          }
                        });

                        // 更新快取
                        hotProductsCache.data = allHotProducts;
                        hotProductsCache.lastUpdate = Date.now();
                      }
                    });
                  }
                }
              });
            }

            res.json({
              total,
              page,
              limit,
              totalPages: Math.ceil(total / limit),
              data: products
            });
          });
        });
      });
    }
  });

  // 獲取商品分類列表
  router.get('/categories', (req, res) => {
    productDb.all(`
      SELECT
        c.id, c.name, c.description, c.parent_id,
        c.display_order, c.is_active
      FROM category c
      WHERE c.is_active = 1
      ORDER BY c.parent_id NULLS FIRST, c.display_order
    `, (err, rows) => {
      if (err) {
        console.error('獲取分類列表失敗:', err);
        logError(err, req, res, '獲取分類列表失敗');
        return res.status(500).json({ error: '獲取分類列表失敗' });
      }

      // 將分類數據轉換為樹形結構
      const categories = rows || [];
      const categoryMap = {};
      const rootCategories = [];

      // 首先將所有分類放入映射表
      categories.forEach(category => {
        categoryMap[category.id] = {
          ...category,
          children: [] // 初始化 children
        };
      });

      // 構建樹狀結構
      categories.forEach(category => {
        const node = categoryMap[category.id];
        if (category.parent_id && categoryMap[category.parent_id]) {
          categoryMap[category.parent_id].children.push(node);
        } else if (!category.parent_id) {
          rootCategories.push(node);
        }
      });

      res.json({
        flat: categories,
        tree: rootCategories
      });
    });
  });

  // 獲取價格類型列表
  router.get('/price-types', (req, res) => {
    productDb.all(`SELECT * FROM price_type WHERE is_active = 1`, (err, priceTypes) => {
      if (err) {
        console.error('獲取價格類型列表失敗:', err);
        logError(err, req, res, '獲取價格類型列表失敗');
        return res.status(500).json({ error: '獲取價格類型列表失敗' });
      }

      res.json(priceTypes);
    });
  });

  // 新增商品
  router.post('/', upload.array('images', 10), (req, res) => {
    // 接收商品資料
    const {
      name, spec, sku, color, category_id, retail_price, cost_price,
      description
    } = req.body;

    // 驗證必填欄位
    if (!name || !spec || !sku || !category_id || !description) {
      return res.status(400).json({ error: '請填寫所有必填欄位' });
    }

    productDb.run('PRAGMA foreign_keys = ON');

    // 使用事務確保資料完整性
    productDb.run('BEGIN TRANSACTION', function(err) {
      if (err) {
        console.error('開始事務失敗:', err);
        logError(err, req, res, '開始事務失敗');
        return res.status(500).json({ error: '新增商品失敗' });
      }

      // 1. 插入商品基本資料
      productDb.run(
        `INSERT INTO product (name, sku, description, spec_name, spec_color, is_active) VALUES (?, ?, ?, ?, ?, 1)`,
        [name, sku, description, spec, color || ''],
        function(err) {
          if (err) {
            console.error('插入商品資料失敗:', err);
            productDb.run('ROLLBACK');
            return res.status(500).json({ error: '插入商品資料失敗' });
          }

          const productId = this.lastID;

          // 3. 插入商品分類關聯
          productDb.run(
            `INSERT INTO product_category (product_id, category_id) VALUES (?, ?)`,
            [productId, category_id],
            function(err) {
              if (err) {
                console.error('插入商品分類關聯失敗:', err);
                productDb.run('ROLLBACK');
                return res.status(500).json({ error: '插入商品分類關聯失敗' });
              }

              // 4. 處理商品圖片上傳
              const uploadedFiles = req.files || [];

              if (uploadedFiles.length > 0) {
                // 建立插入圖片的Promise陣列
                const imagePromises = uploadedFiles.map((file, index) => {
                  return new Promise((resolve, reject) => {
                    const imagePath = `/uploads/products/${file.filename}`;
                    // 第一張圖片設為主圖片
                    const isPrimary = index === 0 ? 1 : 0;

                    productDb.run(
                      `INSERT INTO product_image (product_id, image_path, is_primary, display_order) VALUES (?, ?, ?, ?)`,
                      [productId, imagePath, isPrimary, index],
                      function(err) {
                        if (err) {
                          console.error('插入商品圖片失敗:', err);
                          reject(err);
                        } else {
                          resolve();
                        }
                      }
                    );
                  });
                });

                // 等待所有圖片插入完成
                Promise.all(imagePromises)
                  .then(() => {
                    // 處理價格
                    addProductPrices();
                  })
                  .catch(err => {
                    console.error('插入商品圖片失敗:', err);
                    productDb.run('ROLLBACK');
                    return res.status(500).json({ error: '插入商品圖片失敗' });
                  });
              } else {
                // 沒有圖片，直接處理價格
                addProductPrices();
              }

              // 5. 插入商品價格（如果有）
              function addProductPrices() {
                const currentDate = new Date().toISOString().split('T')[0];
                const promises = [];

                // 零售價
                if (retail_price) {
                  promises.push(new Promise((resolve, reject) => {
                    productDb.run(
                      `INSERT INTO product_price (product_id, price_type_id, price, effective_date)
                     VALUES (?, 2, ?, ?)`,  // 零售價的 price_type_id 為 2
                      [productId, retail_price, currentDate],
                      function(err) {
                        if (err) {
                          console.error('插入零售價失敗:', err);
                          reject(err);
                        } else {
                          resolve();
                        }
                      }
                    );
                  }));
                }

                // 成本價
                if (cost_price) {
                  promises.push(new Promise((resolve, reject) => {
                    productDb.run(
                      `INSERT INTO product_price (product_id, price_type_id, price, effective_date)
                     VALUES (?, 1, ?, ?)`,  // 成本價的 price_type_id 為 1
                      [productId, cost_price, currentDate],
                      function(err) {
                        if (err) {
                          console.error('插入成本價失敗:', err);
                          reject(err);
                        } else {
                          resolve();
                        }
                      }
                    );
                  }));
                }

                // 等待所有價格插入完成
                Promise.all(promises)
                  .then(() => {
                    // 提交事務
                    productDb.run('COMMIT', function(err) {
                      if (err) {
                        console.error('提交事務失敗:', err);
                        logError(err, req, res, '提交事務失敗');
                        return res.status(500).json({ error: '新增商品失敗' });
                      }

                      return res.status(201).json({
                        success: true,
                        message: '商品新增成功',
                        productId
                      });
                    });
                  })
                  .catch(err => {
                    console.error('插入價格資料失敗:', err);
                    productDb.run('ROLLBACK');
                    return res.status(500).json({ error: '插入價格資料失敗' });
                  });
              }
            }
          );
        }
      );
    });
  });

  // 獲取單個商品詳情
  router.get('/:id', (req, res) => {
    const productId = req.params.id;

    // 獲取商品基本資訊
    productDb.get(`SELECT * FROM product WHERE id = ?`, [productId], (err, product) => {
      if (err) {
        console.error('獲取商品詳情失敗:', err);
        logError(err, req, res, '獲取商品詳情失敗');
        return res.status(500).json({ error: '獲取商品詳情失敗' });
      }

      if (!product) {
        return res.status(404).json({ error: '商品不存在' });
      }

      // 獲取商品圖片
      productDb.all(`SELECT * FROM product_image WHERE product_id = ? ORDER BY is_primary DESC, display_order`,
        [productId], (err, images) => {
          if (err) {
            console.error('獲取商品圖片失敗:', err);
          }

          product.images = images || [];

          // 設置主圖片
          if (images && images.length > 0) {
            const primaryImage = images.find(img => img.is_primary) || images[0];
            product.main_image = primaryImage.image_path;
          }

          // 繼續獲取商品規格
          productDb.all(`SELECT * FROM product_spec WHERE product_id = ? ORDER BY display_order`,
            [productId], (err, specs) => {
              if (err) {
                console.error('獲取商品規格失敗:', err);
              }

              product.specs = specs || [];

              // 將規格整合為單一字串
              if (specs && specs.length > 0) {
                // 篩選規格名稱為"規格"的項目
                const specItems = specs.filter(s => s.spec_name === '規格').map(s => s.spec_value);
                // 合併為逗號分隔的字串
                if (specItems.length > 0) {
                  product.spec = specItems.join(',');
                }
              }

              // 獲取商品價格
              productDb.all(`
                SELECT pp.*, pt.name as price_type_name
                FROM product_price pp
                JOIN price_type pt ON pp.price_type_id = pt.id
                WHERE pp.product_id = ?
                ORDER BY pp.effective_date DESC
              `, [productId], (err, prices) => {
                if (err) {
                  console.error('獲取商品價格失敗:', err);
                }

                product.prices = prices || [];

                // 設置零售價格
                if (prices && prices.length > 0) {
                  const retailPrice = prices.find(p => p.price_type_id === 2);
                  if (retailPrice) {
                    product.retail_price = Number(retailPrice.price);
                  }
                }

                // 獲取商品分類
                productDb.all(`
                  SELECT c.*
                  FROM product_category pc
                  JOIN category c ON pc.category_id = c.id
                  WHERE pc.product_id = ?
                `, [productId], (err, categories) => {
                  if (err) {
                    console.error('獲取商品分類失敗:', err);
                  }

                  product.categories = categories || [];

                  // 獲取航線台北倉庫存
                  productDb.get(`
                    SELECT i.quantity, w.name as warehouse_name
                    FROM inventory i
                    JOIN warehouse w ON i.warehouse_id = w.id
                    WHERE i.product_id = ? AND w.name LIKE '%航線台北倉%'
                    LIMIT 1
                  `, [productId], (err, inventory) => {
                    if (err) {
                      console.error('獲取商品庫存失敗:', err);
                    } else if (inventory) {
                      product.stock = inventory.quantity;
                      product.warehouse_name = inventory.warehouse_name;
                    }
                    res.json(product);
                  });
                });
              });
            });
        });
    });
  });

  // 根據SKU查詢商品
  router.get('/by-sku/:sku', (req, res) => {
    const { sku } = req.params;

    if (!sku) {
      return res.status(400).json({ error: '請提供商品SKU' });
    }

    productDb.get(`
      SELECT
        p.*,
        b.name as brand_name,
        COALESCE(
          (SELECT image_path FROM product_image WHERE product_id = p.id AND is_primary = 1 LIMIT 1),
          (SELECT image_path FROM product_image WHERE product_id = p.id ORDER BY display_order ASC LIMIT 1)
        ) as main_image,
        (SELECT price FROM product_price WHERE product_id = p.id AND price_type_id = 2 ORDER BY effective_date DESC LIMIT 1) as retail_price,
        (SELECT price FROM product_price WHERE product_id = p.id AND price_type_id = 1 ORDER BY effective_date DESC LIMIT 1) as cost_price
      FROM product p
      LEFT JOIN brand b ON p.brand_id = b.id
      WHERE p.sku = ? AND p.is_active = 1
    `, [sku], (err, product) => {
      if (err) {
        console.error('根據SKU查詢商品失敗:', err);
        logError(err, req, res, '根據SKU查詢商品失敗');
        return res.status(500).json({ error: '查詢商品失敗' });
      }

      if (!product) {
        return res.status(404).json({ error: '找不到該商品' });
      }

      // 轉換數據類型
      if (product.retail_price) {
        product.retail_price = Number(product.retail_price);
      }

      if (product.cost_price) {
        product.cost_price = Number(product.cost_price);
      }

      res.json(product);
    });
  });

  return router;
};