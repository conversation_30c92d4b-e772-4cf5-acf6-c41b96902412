// 載入統一配置
const config = require('./config.js');
console.log('🔧 環境配置狀態:', config.getConfigStatus());

const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const { createServer } = require('http');
const { Server } = require('socket.io');
const jwt = require('jsonwebtoken'); // JWT needed for middleware
const sqlite3 = require('sqlite3').verbose();

// 庫存異動記錄工具已移除（不再記錄預留相關的異動歷史）

const app = express();
const httpServer = createServer(app);
const io = new Server(httpServer, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

const PORT = config.backend.unified_api.port;
const JWT_SECRET = 'admin-secret-key';

// WebSocket 連接管理
const adminConnections = new Map(); // 儲存管理員連接
const userConnections = new Map(); // 儲存用戶連接
const homepageVisitors = new Map(); // 儲存前台首頁訪客連接

// --- 錯誤處理函數 (不記錄日誌) ---
const logError = (err, req, res, message) => {
  // 只保留錯誤處理邏輯，不記錄日誌
  // 這個函數被許多路由依賴，所以我們保留它但移除日誌記錄功能
};

// WebSocket 身份驗證中間件
const authenticateSocket = (socket, next) => {
  try {
    const token = socket.handshake.auth.token;
    const isVisitor = socket.handshake.auth.isVisitor; // 檢查是否為訪客連接

    // 如果是訪客連接，允許匿名連接
    if (isVisitor) {
      socket.userId = null;
      socket.userRole = 'visitor';
      socket.username = 'visitor';
      socket.isVisitor = true;
      return next();
    }

    if (!token) {
      return next(new Error('未提供授權令牌'));
    }

    jwt.verify(token, JWT_SECRET, (err, decoded) => {
      if (err) {
        return next(new Error('無效的令牌'));
      }

      socket.userId = decoded.userId;
      socket.userRole = decoded.role || 'user'; // 如果沒有角色，預設為普通用戶
      socket.username = decoded.username;
      socket.isVisitor = false;
      next();
    });
  } catch (err) {
    next(new Error('身份驗證失敗'));
  }
};

// WebSocket 連接處理
io.use(authenticateSocket);

io.on('connection', (socket) => {
  console.log(`用戶 ${socket.username} (角色: ${socket.userRole}) 已連接 WebSocket`);

  // 根據用戶角色加入對應的連接池
  if (socket.userRole === 'admin') {
    adminConnections.set(socket.id, socket);
    console.log(`✅ 管理員 ${socket.username} 已加入通知列表，當前管理員連接數: ${adminConnections.size}`);
    console.log(`📋 當前所有管理員連接:`, Array.from(adminConnections.keys()));
  } else if (socket.userRole === 'visitor') {
    // 訪客連接 - 不需要立即加入首頁訪客池，等待 homepage_enter 事件
    console.log(`訪客已連接 WebSocket，連接ID: ${socket.id}`);
  } else {
    // 普通用戶連接
    userConnections.set(socket.userId, socket);
    console.log(`用戶 ${socket.username} (ID: ${socket.userId}) 已加入用戶連接池，當前用戶連接數: ${userConnections.size}`);
  }

  // 處理用戶上線事件（替代原來的首頁進入事件）
  socket.on('user_online', () => {
    if (socket.userRole === 'visitor' || socket.userRole === 'user') {
      homepageVisitors.set(socket.id, {
        socket: socket,
        enterTime: Date.now(),
        userRole: socket.userRole,
        userId: socket.userId || null
      });
      console.log(`用戶上線，當前在線人數: ${homepageVisitors.size}`);
    }
  });

  // 處理用戶下線事件（替代原來的首頁離開事件）
  socket.on('user_offline', () => {
    if (homepageVisitors.has(socket.id)) {
      homepageVisitors.delete(socket.id);
      console.log(`用戶下線，當前在線人數: ${homepageVisitors.size}`);
    }
  });

  // 保持向後兼容性 - 處理舊的首頁進入事件
  socket.on('homepage_enter', () => {
    if (socket.userRole === 'visitor' || socket.userRole === 'user') {
      homepageVisitors.set(socket.id, {
        socket: socket,
        enterTime: Date.now(),
        userRole: socket.userRole,
        userId: socket.userId || null
      });
      console.log(`用戶進入首頁，當前在線人數: ${homepageVisitors.size}`);
    }
  });

  // 保持向後兼容性 - 處理舊的首頁離開事件
  socket.on('homepage_leave', () => {
    if (homepageVisitors.has(socket.id)) {
      homepageVisitors.delete(socket.id);
      console.log(`用戶離開首頁，當前在線人數: ${homepageVisitors.size}`);
    }
  });

  // 處理庫存預留請求（實際扣除庫存）
  socket.on('reserve_stock', (data) => {
    const userId = socket.userId;
    const { cartItems } = data;

    if (!cartItems || !Array.isArray(cartItems) || cartItems.length === 0) {
      socket.emit('reserve_stock_response', {
        success: false,
        error: '購物車項目不能為空'
      });
      return;
    }

    // 檢查是否已有預留記錄
    global.stockReservations = global.stockReservations || new Map();
    if (global.stockReservations.has(userId)) {
      socket.emit('reserve_stock_response', {
        success: false,
        error: '已有庫存預留記錄，請勿重複操作'
      });
      return;
    }

    // 先進行庫存驗證，不使用事務
    let validationCompleted = 0;
    let validationError = false;
    const insufficientItems = [];
    const validatedItems = [];

    cartItems.forEach((item) => {
      // 查詢航線台北倉的當前庫存
      productDb.get(`
        SELECT i.id, i.quantity, i.product_id, p.name as product_name, w.id as warehouse_id
        FROM inventory i
        JOIN warehouse w ON i.warehouse_id = w.id
        JOIN product p ON i.product_id = p.id
        WHERE i.product_id = ? AND w.name LIKE '%航線台北倉%'
        LIMIT 1
      `, [item.id], (err, inventory) => {
        if (err || !inventory) {
          console.error(`查詢商品 ${item.id} 庫存失敗:`, err);
          validationError = true;
          validationCompleted++;
          checkValidationCompletion();
          return;
        }

        // 檢查庫存是否足夠
        if (inventory.quantity < item.quantity) {
          insufficientItems.push({
            id: item.id,
            name: item.name || inventory.product_name,
            requested: item.quantity,
            available: inventory.quantity
          });
        } else {
          validatedItems.push({
            item: item,
            inventory: inventory
          });
        }

        validationCompleted++;
        checkValidationCompletion();
      });
    });

    function checkValidationCompletion() {
      if (validationCompleted === cartItems.length) {
        if (validationError) {
          socket.emit('reserve_stock_response', {
            success: false,
            error: '庫存查詢失敗，請稍後再試'
          });
          return;
        }

        if (insufficientItems.length > 0) {
          socket.emit('reserve_stock_response', {
            success: false,
            error: '部分商品庫存不足',
            insufficientItems: insufficientItems
          });
          return;
        }

        // 開始庫存預留處理
        processStockReservation();
      }
    }

    function processStockReservation() {
      let processedItems = 0;
      let hasError = false;
      const reservationData = [];
      const processErrors = [];

      validatedItems.forEach((validatedItem) => {
        const { item, inventory } = validatedItem;
        const newQuantity = inventory.quantity - item.quantity;

        // 實際扣除庫存（預留）
        productDb.run(`
          UPDATE inventory
          SET quantity = ?, last_updated = datetime('now', '+8 hours')
          WHERE id = ?
        `, [newQuantity, inventory.id], function(updateErr) {
          if (updateErr) {
            console.error(`更新商品 ${item.id} 庫存失敗:`, updateErr);
            hasError = true;
            processErrors.push(`商品 ${item.id} 預留失敗`);
            processedItems++;
            checkProcessCompletion();
            return;
          }

          // 記錄預留信息（不記錄庫存異動歷史）
          reservationData.push({
            productId: item.id,
            productName: inventory.product_name,
            quantity: item.quantity,
            inventoryId: inventory.id,
            warehouseId: inventory.warehouse_id,
            previousQuantity: inventory.quantity,
            newQuantity: newQuantity
          });

          console.log(`✅ 商品 ${item.id} 庫存預留成功: ${inventory.quantity} → ${newQuantity}`);
          processedItems++;
          checkProcessCompletion();
        });
      });

      function checkProcessCompletion() {
        if (processedItems === validatedItems.length) {
          if (hasError) {
            socket.emit('reserve_stock_response', {
              success: false,
              error: '庫存預留失敗',
              details: processErrors
            });
            return;
          }

          // 儲存預留記錄到記憶體
          global.stockReservations.set(userId, {
            reservations: reservationData,
            timestamp: Date.now(),
            socketId: socket.id,
            type: 'actual_reserve'
          });

          // 設置3分鐘後自動回補庫存
          setTimeout(() => {
            autoRestoreStock(userId);
          }, 180000); // 3分鐘

          socket.emit('reserve_stock_response', {
            success: true,
            message: '庫存已預留三分鐘，請盡快完成訂單',
            reservedItems: reservationData.length
          });

          console.log(`庫存預留完成 - 用戶: ${userId}, 項目數: ${reservationData.length}`);
        }
      }
    }
  });

  // 處理庫存回補請求（實際回補庫存）
  socket.on('restore_stock', (data) => {
    const userId = socket.userId;
    restoreUserStock(userId, 'manual_restore');
  });

  // 自動回補庫存（10秒超時）
  function autoRestoreStock(userId) {
    restoreUserStock(userId, 'timeout_restore');
  }

  // 回補用戶庫存的通用函數
  function restoreUserStock(userId, restoreType = 'manual_restore') {
    global.stockReservations = global.stockReservations || new Map();
    const userReservation = global.stockReservations.get(userId);

    if (!userReservation || userReservation.type !== 'actual_reserve') {
      // 沒有實際的預留記錄，直接返回成功
      const targetSocket = io.sockets.sockets.get(userReservation?.socketId);
      if (targetSocket) {
        targetSocket.emit('restore_stock_response', {
          success: true,
          message: '沒有需要回補的庫存預留'
        });
      }
      return;
    }

    // 檢查是否已經在處理中，避免重複處理
    if (userReservation.isRestoring) {

      return;
    }

    // 標記為正在處理
    userReservation.isRestoring = true;

    const { reservations } = userReservation;

    if (reservations.length === 0) {
      global.stockReservations.delete(userId);

      const targetSocket = io.sockets.sockets.get(userReservation.socketId);
      if (targetSocket) {
        targetSocket.emit('restore_stock_response', {
          success: true,
          message: '沒有需要回補的庫存'
        });
      }
      return;
    }

    // 逐個處理庫存回補，不使用事務以避免衝突
    let processedItems = 0;
    let hasError = false;
    const restoreErrors = [];

    reservations.forEach((reservation, index) => {
      // 回補庫存
      const restoredQuantity = reservation.previousQuantity;
      productDb.run(`
        UPDATE inventory
        SET quantity = ?, last_updated = datetime('now', '+8 hours')
        WHERE id = ?
      `, [restoredQuantity, reservation.inventoryId], function(updateErr) {
        if (updateErr) {
          console.error(`回補商品 ${reservation.productId} 庫存失敗:`, updateErr);
          hasError = true;
          restoreErrors.push(`商品 ${reservation.productId} 回補失敗`);
          processedItems++;
          checkCompletion();
          return;
        }

        // 庫存回補成功（不記錄庫存異動歷史）
        console.log(`✅ 商品 ${reservation.productId} 庫存回補成功: ${reservation.newQuantity} → ${restoredQuantity}`);
        processedItems++;
        checkCompletion();
      });
    });

    function checkCompletion() {
      if (processedItems === reservations.length) {
        // 清除預留記錄
        global.stockReservations.delete(userId);

        const targetSocket = io.sockets.sockets.get(userReservation.socketId);
        if (targetSocket) {
          if (hasError) {
            targetSocket.emit('restore_stock_response', {
              success: false,
              error: '部分庫存回補失敗',
              details: restoreErrors
            });
          } else {
            targetSocket.emit('restore_stock_response', {
              success: true,
              message: `庫存已回補 (${restoreType === 'timeout_restore' ? '超時自動' :
                        restoreType === 'disconnect_restore' ? '斷線自動' : '手動'})`
            });
          }
        }

        console.log(`庫存回補完成 - 用戶: ${userId}, 類型: ${restoreType}, 項目數: ${reservations.length}`);
      }
    }
  }

  // 處理錯誤
  socket.on('error', (error) => {
    console.error(`WebSocket 錯誤 (${socket.username}):`, error);
  });

  // 斷線時清理用戶的庫存預留和首頁訪客記錄
  socket.on('disconnect', () => {
    const userId = socket.userId;
    if (userId) {
      restoreUserStock(userId, 'disconnect_restore');
    }

    // 清理首頁訪客記錄
    if (homepageVisitors.has(socket.id)) {
      homepageVisitors.delete(socket.id);
      console.log(`用戶斷線，已從首頁訪客列表移除，當前首頁在線人數: ${homepageVisitors.size}`);
    }

    // 清理其他連接池
    if (socket.userRole === 'admin') {
      adminConnections.delete(socket.id);
    } else if (socket.userId) {
      userConnections.delete(socket.userId);
    }

    console.log(`用戶 ${socket.username} (角色: ${socket.userRole}) 已斷開 WebSocket 連接`);
  });
});

// 將 io 實例和連接池傳遞給路由使用
global.socketIO = io;
global.adminConnections = adminConnections;
global.userConnections = userConnections;
global.homepageVisitors = homepageVisitors;

// --- 資料庫連接 ---
// 根據環境配置決定資料庫路徑
const dbBasePath = config.database.useLocal
  ? path.join(__dirname, 'database')
  : path.join(__dirname, 'database'); // 目前都使用本地 SQLite，未來可擴展為遠端資料庫

const userDbFile = path.join(dbBasePath, 'user.db');
const settingsDbFile = path.join(dbBasePath, 'settings.db');
const productDbFile = path.join(dbBasePath, 'product.db');

console.log(`📁 資料庫路徑: ${dbBasePath}`);

// 確保 database 目錄存在
if (!fs.existsSync(dbBasePath)) {
  try {
    fs.mkdirSync(dbBasePath);
    console.log(`已創建 database 目錄: ${dbBasePath}`);
  } catch (mkdirErr) {
    console.error(`無法創建 database 目錄 ${dbBasePath}:`, mkdirErr);
    // 根據情況決定是否退出進程
    process.exit(1);
  }
}

let userDb;
let settingsDb;
let productDb;
let theoneDb;

async function connectDatabases() {
  const connect = (file, name) => new Promise((resolve, reject) => {
    const dbConn = new sqlite3.Database(file, sqlite3.OPEN_READWRITE | sqlite3.OPEN_CREATE, (err) => { // Ensure DB is created if not exists
      if (err) {
        console.error(`連接到 ${name} 數據庫 (${file}) 時發生錯誤:`, err);
        reject(new Error(`無法連接 ${name} DB: ${err.message}`));
      } else {
        console.log(`已連接到 ${name} 數據庫: ${file}`);
        if (name === '用戶' || name === '產品') {
           dbConn.run('PRAGMA foreign_keys = ON', fkErr => {
              if (fkErr) console.error(`啟用 ${name} DB 外鍵約束失敗:`, fkErr);
           });
        }
        resolve(dbConn);
      }
    });
  });

  try {
    // Use Promise.all for potentially faster parallel connections
    [userDb, settingsDb, productDb, theoneDb] = await Promise.all([
        connect(userDbFile, '用戶'),
        connect(settingsDbFile, '設定'),
        connect(productDbFile, '產品'),
        connect(path.join(__dirname, 'database', 'theone.db'), 'THE ONE 3C')
    ]);
    console.log('所有資料庫連接已建立。');

    // 設定全域資料庫連接供其他模組使用
    global.userDb = userDb;
    global.settingsDb = settingsDb;
    global.productDb = productDb;
    global.theoneDb = theoneDb;

    // 初始化資料庫表格
    const { initializeUserDatabase, initializeProductDatabase, initializeSettingsDatabase, initializeTheOneDatabase } = require('./database/init');
    initializeUserDatabase(userDb);
    initializeProductDatabase(productDb);
    initializeSettingsDatabase(settingsDb);
    initializeTheOneDatabase(theoneDb);

    // 初始化定時任務服務
    const schedulerService = require('./services/schedulerService');
    schedulerService.init(productDb, theoneDb);

    // 啟動週發票開立定時任務
    schedulerService.start('weeklyInvoice');

    // 啟動 THE ONE 3C 自動化定時任務
    schedulerService.start('theoneAutomation');

    console.log('定時任務服務已啟動');

    return true;
  } catch (error) {
    // The error from Promise.all will be the first rejection
    logError(error, null, null, '建立資料庫連接失敗');
    // Clean up potentially opened connections if some succeeded before failure
    if (userDb) userDb.close();
    if (settingsDb) settingsDb.close();
    if (productDb) productDb.close();
    if (theoneDb) theoneDb.close();
    return false;
  }
}

// --- 中間件設定 ---
// 動態 CORS 設定
const allowedOrigins = [
  // 本地開發環境
  'http://localhost:4015',
  'http://localhost:4016',
  'http://localhost:5173',
  'http://localhost:5174',
  'http://127.0.0.1:4015',
  'http://127.0.0.1:4016',
  'http://127.0.0.1:5173',
  'http://127.0.0.1:5174',
  // 生產環境
  'http://************',
  'http://************:8080',
  'http://************:8889',
  'http://************:4016',
  // 其他可能的本地 IP
  'http://**************:4015',
  'http://**************:4016'
];

app.use(cors({
  origin: allowedOrigins,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(express.json());

// 添加靜態文件服務
app.use('/uploads', express.static(path.join(__dirname, 'public', 'uploads')));

// 臨時添加請求日誌中間件用於調試
// app.use((req, res, next) => {
//   console.log(`=== DEBUG REQUEST: ${req.method} ${req.url} ===`);
//   console.log('Headers:', req.headers.authorization ? 'Has auth header' : 'No auth header');
//   next();
// });

// --- 驗證中間件 ---

// 驗證管理員權限中間件
const authenticateAdmin = (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: '未提供有效的授權令牌 (Bearer)' });
    }
    const token = authHeader.split(' ')[1];

    jwt.verify(token, JWT_SECRET, (err, decoded) => {
      if (err) {
        console.error('JWT 驗證失敗:', err.name, err.message);
        let errorMsg = '無效的令牌';
        if (err.name === 'TokenExpiredError') errorMsg = '令牌已過期';
        else if (err.name === 'JsonWebTokenError') errorMsg = '令牌格式錯誤';
        return res.status(401).json({ error: errorMsg });
      }
      // 允許所有在admins表中的用戶訪問後台（admin 或 staff）
      if (!decoded.role || (decoded.role !== 'admin' && decoded.role !== 'staff')) {
        return res.status(403).json({ error: '沒有管理員權限' });
      }
      req.user = decoded;
      next();
    });
  } catch (err) {
    logError(err, req, res, '權限驗證中間件錯誤');
    return res.status(500).json({ error: '伺服器內部錯誤' });
  }
};

// 驗證普通用戶權限中間件
const auth = (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: '未提供有效的授權令牌 (Bearer)' });
    }
    const token = authHeader.split(' ')[1];
    jwt.verify(token, JWT_SECRET, (err, decoded) => {
      if (err) {
        console.error('JWT 驗證失敗:', err.name, err.message);
        let errorMsg = '無效的令牌';
        if (err.name === 'TokenExpiredError') errorMsg = '令牌已過期';
        else if (err.name === 'JsonWebTokenError') errorMsg = '令牌格式錯誤';
        return res.status(401).json({ error: errorMsg });
      }

      // 將解碼後的用戶信息添加到請求對象，並確保可以通過req.user.id訪問用戶ID
      req.user = {
        id: decoded.userId || decoded.id, // 兼容不同的令牌格式
        username: decoded.username,
        ...decoded
      };

      next();
    });
  } catch (err) {
    logError(err, req, res, '權限驗證中間件錯誤');
    return res.status(500).json({ error: '伺服器內部錯誤' });
  }
};

// --- 路由掛載 ---
// Note: Routes are required after database connections might be established in startServer
// We define the mounting logic here, but the require calls could be inside startServer

function mountRoutes(dependencies) {
    console.log('掛載路由...');
    try {
        console.log('載入前台認證路由...');
        const frontendAuthRoutes = require('./routes/frontend_routes/auth');
        console.log('載入前台雜項路由...');
        const frontendMiscRoutes = require('./routes/frontend_routes/misc');
        console.log('載入後台認證路由...');
        const adminAuthRoutes = require('./routes/admin_routes/auth');
        console.log('載入後台用戶路由...');
        const adminUserRoutes = require('./routes/admin_routes/users');
        console.log('載入後台管理員路由...');
        const adminAdminRoutes = require('./routes/admin_routes/admins');
        console.log('載入後台分類路由...');
        const adminCategoryRoutes = require('./routes/admin_routes/categories');
        console.log('載入後台設定路由...');
        const adminSettingsRoutes = require('./routes/admin_routes/settings'); // 啟用設定路由
        console.log('載入後台產品路由...');
        const adminProductRoutes = require('./routes/admin_routes/products')({ productDb: dependencies.productDb, logError, authenticateAdmin });
        console.log('載入前台產品路由...');
        const productRoutes = require('./routes/productRoutes'); // 前台產品路由
        console.log('載入後台庫存路由...');
        const adminInventoryRoutes = require('./routes/admin_routes/inventory'); // 加載庫存管理路由
        console.log('載入後台盤點路由...');
        const adminInventoryCountRoutes = require('./routes/admin_routes/inventory_count'); // 加載盤點管理路由
        console.log('載入後台進貨路由...');
        const adminPurchaseRoutes = require('./routes/admin_routes/purchase'); // 添加進貨管理路由
        console.log('載入後台進貨單路由...');
        const adminPurchaseOrderRoutes = require('./routes/admin_routes/purchase_order'); // 添加進貨單管理路由
        console.log('載入後台廠商路由...');
        const adminSupplierRoutes = require('./routes/admin_routes/suppliers'); // 添加廠商管理路由
        console.log('載入購物車路由...');
        const cartRoutes = require('./routes/cartRoutes'); // 新增購物車路由
        console.log('載入訂單路由...');
        const orderRoutes = require('./routes/orderRoutes'); // 新增訂單路由
        console.log('載入收貨地址路由...');
        const addressRoutes = require('./routes/addressRoutes'); // 新增收貨地址路由
        console.log('載入後台訂單路由...');
        const adminOrderRoutes = require('./routes/admin_routes/orders'); // 新增管理員訂單路由
        console.log('載入產品搜尋路由...');
        const productSearchRoutes = require('./routes/frontend_routes/product_search')({ productDb: dependencies.productDb, logError });
        console.log('載入紅利點數路由...');
        const bonusPointsRoutes = require('./routes/frontend_routes/bonus_points'); // 新增紅利點數路由
        console.log('載入發票路由...');
        const invoiceRoutes = require('./routes/invoiceRoutes'); // 新增發票路由
        console.log('載入後台發票路由...');
        const adminInvoiceRoutes = require('./routes/admin_routes/invoices'); // 新增後台發票路由
        console.log('載入後台產品代尋路由...');
        const adminProductRequestRoutes = require('./routes/admin_routes/product_requests'); // 新增產品代尋路由
        console.log('載入前台產品代尋路由...');
        const frontendProductRequestRoutes = require('./routes/frontend_routes/product_requests'); // 新增前台產品代尋路由
        console.log('載入前台公告路由...');
        const frontendAnnouncementRoutes = require('./routes/frontend_routes/announcement'); // 新增前台公告路由
        console.log('載入後台公告路由...');
        const adminAnnouncementRoutes = require('./routes/admin_routes/announcement'); // 新增後台公告路由
        console.log('載入後台上傳路由...');
        const adminUploadRoutes = require('./routes/admin_routes/upload'); // 新增後台上傳路由
        console.log('載入前台退貨申請路由...');
        const frontendReturnRequestRoutes = require('./routes/frontend_routes/return_requests'); // 新增前台退貨申請路由
        console.log('載入後台退貨申請路由...');
        const adminReturnRequestRoutes = require('./routes/admin_routes/return_requests'); // 新增後台退貨申請路由
        console.log('載入後台成本計算器路由...');
        const adminCostRoutes = require('./routes/admin_routes/cost'); // 新增後台成本計算器路由
        console.log('載入後台順豐快遞路由...');
        const adminSfExpressRoutes = require('./routes/admin_routes/sfExpress'); // 新增後台順豐快遞路由
        console.log('載入後台在線用戶路由...');
        const adminOnlineUsersRoutes = require('./routes/admin_routes/online_users'); // 新增後台在線用戶路由
        console.log('載入前台意見反饋路由...');
        const frontendFeedbackRoutes = require('./routes/frontend_routes/feedback'); // 新增前台意見反饋路由
        console.log('載入後台意見反饋路由...');
        const adminFeedbackRoutes = require('./routes/admin_routes/feedback'); // 新增後台意見反饋路由
        console.log('載入後台 THE ONE 3C 自動化路由...');
        const adminTheOneRoutes = require('./routes/admin_routes/theone'); // 新增 THE ONE 3C 自動化路由
        console.log('載入後台比價路由...');
        const adminPriceComparisonRoutes = require('./routes/admin_routes/priceComparison'); // 新增比價管理路由
        console.log('載入後台商品比較路由...');
        

        // 確保依賴對象包含所有必要的資料庫連接
        const routeDependencies = {
            ...dependencies,
            db: dependencies.userDb,  // 為了向前兼容，保留 db
            userDb: dependencies.userDb,
            productDb: dependencies.productDb,
            settingsDb: dependencies.settingsDb,
            theoneDb: dependencies.theoneDb,  // 添加 THE ONE 3C 資料庫
            auth: dependencies.auth,  // 添加auth中間件
            adminAuth: dependencies.authenticateAdmin,  // 添加adminAuth中間件
            logError
        };

        // 掛載前台路由
        console.log('掛載前台認證路由...');
        app.use('/api', frontendAuthRoutes(routeDependencies));
        console.log('掛載前台雜項路由...');
        console.log('🔧 routeDependencies包含:', Object.keys(routeDependencies));
        console.log('🔧 settingsDb狀態:', routeDependencies.settingsDb ? '✅ 已傳遞' : '❌ 未傳遞');
        app.use('/', frontendMiscRoutes(routeDependencies)); // 確保這裡的dependencies包含settingsDb
        console.log('✅ 前台雜項路由掛載完成，已掛載到根路徑 "/" ');
        console.log('掛載前台產品路由...');
        app.use('/api/products', productRoutes(routeDependencies)); // 前台產品路由需要傳遞依賴
        console.log('掛載購物車路由...');
        app.use('/api/cart', cartRoutes(routeDependencies)); // 註冊購物車路由
        console.log('掛載訂單路由...');
        app.use('/api/orders', orderRoutes(routeDependencies)); // 註冊訂單路由
        console.log('掛載收貨地址路由...');
        app.use('/api', addressRoutes(routeDependencies)); // 註冊收貨地址路由
        console.log('掛載紅利點數路由...');
        app.use('/api', bonusPointsRoutes(routeDependencies)); // 註冊紅利點數路由
        console.log('掛載發票路由...');
        app.use('/api', invoiceRoutes(routeDependencies)); // 註冊發票路由
        console.log('掛載前台產品代尋路由...');
        app.use('/api/product-requests', frontendProductRequestRoutes(routeDependencies)); // 註冊前台產品代尋路由
        console.log('掛載前台公告路由...');
        app.use('/api/announcement', frontendAnnouncementRoutes); // 註冊前台公告路由
        console.log('掛載前台退貨申請路由...');
        app.use('/api', frontendReturnRequestRoutes(routeDependencies)); // 註冊前台退貨申請路由
        console.log('掛載前台意見反饋路由...');
        app.use('/api/feedback', frontendFeedbackRoutes(routeDependencies)); // 註冊前台意見反饋路由

        console.log('掛載後台路由...');
        // 掛載後台路由 - /api/admin 路徑
        console.log('掛載後台認證路由...');
        app.use('/api/admin', adminAuthRoutes(routeDependencies));
        console.log('掛載後台用戶路由...');
        app.use('/api/admin', adminUserRoutes(routeDependencies));
        console.log('掛載後台管理員路由...');
        app.use('/api/admin', adminAdminRoutes(routeDependencies));
        console.log('掛載後台分類路由...');
        app.use('/api/admin/categories', adminCategoryRoutes(routeDependencies));
        console.log('掛載後台設定路由...');
        app.use('/api/admin/settings', adminSettingsRoutes(routeDependencies)); // 啟用設定路由
        console.log('掛載後台產品路由...');
        app.use('/api/admin/products', adminProductRoutes);
        console.log('掛載後台庫存路由...');
        app.use('/api/admin/inventory', adminInventoryRoutes(routeDependencies)); // 修改：添加庫存管理路由，明確指定路徑前綴
        console.log('掛載後台盤點路由...');
        app.use('/api/admin/inventory-count', adminInventoryCountRoutes(routeDependencies)); // 添加盤點管理路由
        console.log('掛載後台進貨路由...');
        app.use('/api/admin/purchase', adminPurchaseRoutes(routeDependencies)); // 添加進貨管理路由
        console.log('掛載後台進貨單路由...');
        app.use('/api/admin/purchase-orders', adminPurchaseOrderRoutes(routeDependencies)); // 添加進貨單管理路由
        console.log('掛載後台廠商路由...');
        app.use('/api/admin/suppliers', adminSupplierRoutes(routeDependencies)); // 添加廠商管理路由
        console.log('掛載後台訂單路由...');
        app.use('/api/admin/orders', adminOrderRoutes(routeDependencies)); // 添加訂單管理路由
        console.log('掛載後台產品代尋路由...');
        app.use('/api/admin/product-requests', adminProductRequestRoutes(routeDependencies)); // 添加產品代尋管理路由
        console.log('掛載後台公告路由...');
        app.use('/api/admin/announcement', adminAnnouncementRoutes); // 添加後台公告管理路由
        console.log('掛載後台上傳路由...');
        app.use('/api/admin/upload', adminUploadRoutes); // 添加後台上傳路由
        console.log('掛載後台退貨申請路由...');
        app.use('/api/admin', adminReturnRequestRoutes(routeDependencies)); // 添加後台退貨申請管理路由
        console.log('掛載後台成本計算器路由...');
        app.use('/api/admin/cost', authenticateAdmin, adminCostRoutes); // 添加後台成本計算器路由（需要管理員認證）
        console.log('掛載後台順豐快遞路由...');
        app.use('/api/admin/sf-express', adminSfExpressRoutes(routeDependencies)); // 添加後台順豐快遞路由
        console.log('掛載後台在線用戶路由...');
        app.use('/api/admin/online-users', adminOnlineUsersRoutes(routeDependencies)); // 添加後台在線用戶路由
        console.log('掛載後台發票路由...');
        app.use('/api/admin/invoices', adminInvoiceRoutes(routeDependencies)); // 添加後台發票管理路由
        console.log('掛載後台意見反饋路由...');
        app.use('/api/admin/feedback', adminFeedbackRoutes(routeDependencies)); // 添加後台意見反饋管理路由
        console.log('掛載後台 THE ONE 3C 自動化路由...');
        app.use('/api/admin/theone', adminTheOneRoutes(routeDependencies)); // 添加 THE ONE 3C 自動化管理路由
        console.log('掛載後台比價路由...');
        app.use('/api/admin/price-comparison', authenticateAdmin, adminPriceComparisonRoutes); // 添加比價管理路由
        console.log('掛載後台商品比較路由...');
        

        // 掛載後台路由別名 - /admin 路徑（向前兼容）
        console.log('掛載後台路由別名 (/admin)...');
        app.use('/admin', adminAuthRoutes(routeDependencies));
        app.use('/admin', adminUserRoutes(routeDependencies));
        app.use('/admin', adminAdminRoutes(routeDependencies));
        app.use('/admin/categories', adminCategoryRoutes(routeDependencies));
        app.use('/admin/settings', adminSettingsRoutes(routeDependencies));
        app.use('/admin/products', adminProductRoutes);
        app.use('/admin/inventory', adminInventoryRoutes(routeDependencies));
        app.use('/admin/inventory-count', adminInventoryCountRoutes(routeDependencies));
        app.use('/admin/purchase', adminPurchaseRoutes(routeDependencies));
        app.use('/admin/purchase-orders', adminPurchaseOrderRoutes(routeDependencies));
        app.use('/admin/suppliers', adminSupplierRoutes(routeDependencies));
        app.use('/admin/orders', adminOrderRoutes(routeDependencies));
        app.use('/admin/product-requests', adminProductRequestRoutes(routeDependencies));
        app.use('/admin/announcement', adminAnnouncementRoutes);
        app.use('/admin/upload', adminUploadRoutes);
        app.use('/admin', adminReturnRequestRoutes(routeDependencies));
        app.use('/admin/cost', authenticateAdmin, adminCostRoutes);
        app.use('/admin/sf-express', adminSfExpressRoutes(routeDependencies));
        app.use('/admin/online-users', adminOnlineUsersRoutes(routeDependencies));
        app.use('/admin/invoices', adminInvoiceRoutes(routeDependencies));
        app.use('/admin/feedback', adminFeedbackRoutes(routeDependencies));
        app.use('/admin/theone', adminTheOneRoutes(routeDependencies));
        app.use('/admin/price-comparison', authenticateAdmin, adminPriceComparisonRoutes);
        
        console.log('掛載產品搜尋路由...');
        app.use('/api/product-search', productSearchRoutes);
        console.log('路由掛載完成！');

    } catch (routeError) {
        logError(routeError, null, null, '加載或掛載路由時出錯');
        throw routeError; // Rethrow to stop server startup if routes fail
    }
}

// --- 錯誤處理和 404 ---
function setupErrorHandlers() {
    // 404 處理
    app.use((req, res) => {
        res.status(404).json({ error: '找不到請求的資源' });
    });

    // 全局錯誤處理
    app.use((err, req, res, next) => {
        console.error('全局錯誤處理:', err);
        res.status(500).json({ error: '伺服器內部錯誤' });
    });
}

// --- 伺服器啟動 ---
async function startServer() {
  console.log('正在建立資料庫連接...');
  const dbConnected = await connectDatabases();

  if (!dbConnected) {
    console.error('無法啟動伺服器：資料庫連接失敗。');
    process.exit(1);
  }

  // 建立依賴對象
  const dependencies = {
    db: userDb, // 為了向後兼容
    userDb,
    productDb,
    settingsDb,
    theoneDb, // 添加 THE ONE 3C 資料庫
    categoryDb: productDb, // 添加 categoryDb 依賴，將其設為與 productDb 相同
    logError,
    authenticateAdmin,
    auth, // 添加普通用戶驗證中間件
    JWT_SECRET,
    bcrypt: require('bcryptjs') // Pass bcrypt module directly
  };

  // 掛載路由
  try {
    console.log('開始掛載路由...');
    mountRoutes(dependencies);
    console.log('路由掛載成功！');
  } catch (mountError) {
    logError(mountError, null, null, '掛載路由時出錯');
    console.error('掛載路由時出錯:', mountError);
    process.exit(1);
  }

  // 設置錯誤處理
  console.log('設置錯誤處理...');
  setupErrorHandlers();
  console.log('錯誤處理設置完成！');

  // 啟動監聽
  console.log('啟動伺服器監聽...');
  const server = httpServer.listen(PORT, '0.0.0.0', () => {
    console.log(`伺服器已啟動，運行在端口 ${PORT}，監聽所有網路介面`);
  }).on('error', (error) => {
    logError(error, null, null, '伺服器監聽錯誤');
    // 在這裡關閉資料庫連接可能是個好主意
    if (userDb) userDb.close();
    if (settingsDb) settingsDb.close();
    if (productDb) productDb.close();
    if (theoneDb) theoneDb.close();
    process.exit(1);
  });
}

// 執行啟動
startServer();
